# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-27 22:40+0200\n"
"PO-Revision-Date: 2019-11-05 00:38+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: <PERSON><PERSON><PERSON> (http://www.transifex.com/django/django/language/"
"kab/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: kab\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Afrikaans"
msgstr "Tafrikanst"

msgid "Arabic"
msgstr "Taɛṛabt"

msgid "Asturian"
msgstr "Tasturyant"

msgid "Azerbaijani"
msgstr "Tazeṛbayǧant"

msgid "Bulgarian"
msgstr "Tabulgarit"

msgid "Belarusian"
msgstr "Tabilurusit"

msgid "Bengali"
msgstr "Tabelgalit"

msgid "Breton"
msgstr "Tabrutunt"

msgid "Bosnian"
msgstr "Tabusnit"

msgid "Catalan"
msgstr "Takaṭalant"

msgid "Czech"
msgstr "Tačikit"

msgid "Welsh"
msgstr "Takusit"

msgid "Danish"
msgstr "Tadanit"

msgid "German"
msgstr "Talmanit"

msgid "Lower Sorbian"
msgstr "Tasiṛbit n wadda"

msgid "Greek"
msgstr "Tagrigit"

msgid "English"
msgstr "Taglizit"

msgid "Australian English"
msgstr "Taglizit n Ustralya"

msgid "British English"
msgstr "Taglizit (UK)"

msgid "Esperanto"
msgstr "Taspirantit"

msgid "Spanish"
msgstr "Taspanit"

msgid "Argentinian Spanish"
msgstr "Taspanit n Arjuntin"

msgid "Colombian Spanish"
msgstr "Taspanit n Kulumbya"

msgid "Mexican Spanish"
msgstr "Taspanit n Miksik"

msgid "Nicaraguan Spanish"
msgstr "Taspanit n Nikaragwa"

msgid "Venezuelan Spanish"
msgstr "Taspanit n Vinizwila"

msgid "Estonian"
msgstr "Tastunit"

msgid "Basque"
msgstr "Tabaskit"

msgid "Persian"
msgstr "Tafarsit"

msgid "Finnish"
msgstr "Tafinit"

msgid "French"
msgstr "Tafṛansist"

msgid "Frisian"
msgstr ""

msgid "Irish"
msgstr ""

msgid "Scottish Gaelic"
msgstr ""

msgid "Galician"
msgstr ""

msgid "Hebrew"
msgstr ""

msgid "Hindi"
msgstr "Tahendit"

msgid "Croatian"
msgstr "Takarwasit"

msgid "Upper Sorbian"
msgstr ""

msgid "Hungarian"
msgstr "Tahungarit"

msgid "Armenian"
msgstr ""

msgid "Interlingua"
msgstr ""

msgid "Indonesian"
msgstr "Tandunizit"

msgid "Ido"
msgstr ""

msgid "Icelandic"
msgstr "Taslandit"

msgid "Italian"
msgstr "Taṭelyanit"

msgid "Japanese"
msgstr ""

msgid "Georgian"
msgstr "Tajyuṛjit"

msgid "Kabyle"
msgstr ""

msgid "Kazakh"
msgstr "Takazaxt"

msgid "Khmer"
msgstr ""

msgid "Kannada"
msgstr "Takannadat"

msgid "Korean"
msgstr "Takurit"

msgid "Luxembourgish"
msgstr ""

msgid "Lithuanian"
msgstr "Talitwanit"

msgid "Latvian"
msgstr "Talitunit"

msgid "Macedonian"
msgstr "Tamasidunit"

msgid "Malayalam"
msgstr "Tamayalamt"

msgid "Mongolian"
msgstr ""

msgid "Marathi"
msgstr ""

msgid "Burmese"
msgstr "Tabirmanit"

msgid "Norwegian Bokmål"
msgstr ""

msgid "Nepali"
msgstr "Tanipalit"

msgid "Dutch"
msgstr "Tahulandit"

msgid "Norwegian Nynorsk"
msgstr ""

msgid "Ossetic"
msgstr ""

msgid "Punjabi"
msgstr "Tabenjabit"

msgid "Polish"
msgstr "Tapulandit"

msgid "Portuguese"
msgstr "Tapurtugit"

msgid "Brazilian Portuguese"
msgstr ""

msgid "Romanian"
msgstr "Tarumanit"

msgid "Russian"
msgstr "Tarusit"

msgid "Slovak"
msgstr "Tasluvakt"

msgid "Slovenian"
msgstr ""

msgid "Albanian"
msgstr "Talbanit"

msgid "Serbian"
msgstr "Tasiṛbit"

msgid "Serbian Latin"
msgstr ""

msgid "Swedish"
msgstr "Taswidit"

msgid "Swahili"
msgstr "Taswahilit"

msgid "Tamil"
msgstr "Taṭamult"

msgid "Telugu"
msgstr ""

msgid "Thai"
msgstr ""

msgid "Turkish"
msgstr "Taṭurkit"

msgid "Tatar"
msgstr ""

msgid "Udmurt"
msgstr ""

msgid "Ukrainian"
msgstr ""

msgid "Urdu"
msgstr ""

msgid "Uzbek"
msgstr ""

msgid "Vietnamese"
msgstr ""

msgid "Simplified Chinese"
msgstr ""

msgid "Traditional Chinese"
msgstr ""

msgid "Messages"
msgstr "Iznan"

msgid "Site Maps"
msgstr ""

msgid "Static Files"
msgstr ""

msgid "Syndication"
msgstr ""

msgid "That page number is not an integer"
msgstr ""

msgid "That page number is less than 1"
msgstr ""

msgid "That page contains no results"
msgstr ""

msgid "Enter a valid value."
msgstr "Sekcem azal ameɣtu."

msgid "Enter a valid URL."
msgstr ""

msgid "Enter a valid integer."
msgstr ""

msgid "Enter a valid email address."
msgstr "Sekcem tansa imayl tameɣtut."

#. Translators: "letters" means latin letters: a-z and A-Z.
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""

msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""

msgid "Enter a valid IPv4 address."
msgstr "Sekcem tansa IPv4 tameɣtut."

msgid "Enter a valid IPv6 address."
msgstr ""

msgid "Enter a valid IPv4 or IPv6 address."
msgstr ""

msgid "Enter only digits separated by commas."
msgstr ""

#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""

#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr ""

#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr ""

#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

msgid "Enter a number."
msgstr "Sekcem amḍan."

#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""

msgid "Null characters are not allowed."
msgstr ""

msgid "and"
msgstr "akked"

#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr ""

#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr ""

msgid "This field cannot be null."
msgstr ""

msgid "This field cannot be blank."
msgstr ""

#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr ""

#. Translators: The 'lookup_type' is one of 'date', 'year' or 'month'.
#. Eg: "Title must be unique for pub_date year"
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""

#, python-format
msgid "Field of type: %(field_type)s"
msgstr ""

#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr ""

#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr ""

msgid "Boolean (Either True or False)"
msgstr ""

#, python-format
msgid "String (up to %(max_length)s)"
msgstr ""

msgid "Comma-separated integers"
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""

msgid "Date (without time)"
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD HH:MM[:ss[."
"uuuuuu]][TZ] format."
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""

msgid "Date (with time)"
msgstr "Azemz (s wakud)"

#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr ""

msgid "Decimal number"
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] [[HH:]MM:]ss[."
"uuuuuu] format."
msgstr ""

msgid "Duration"
msgstr "Tanzagt"

msgid "Email address"
msgstr "Tansa email"

msgid "File path"
msgstr "Abrid n ufaylu"

#, python-format
msgid "“%(value)s” value must be a float."
msgstr ""

msgid "Floating point number"
msgstr ""

#, python-format
msgid "“%(value)s” value must be an integer."
msgstr ""

msgid "Integer"
msgstr "Ummid"

msgid "Big (8 byte) integer"
msgstr ""

msgid "IPv4 address"
msgstr ""

msgid "IP address"
msgstr "Tansa  IP"

#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr ""

msgid "Boolean (Either True, False or None)"
msgstr ""

msgid "Positive integer"
msgstr ""

msgid "Positive small integer"
msgstr ""

#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr ""

msgid "Small integer"
msgstr ""

msgid "Text"
msgstr "Aḍris"

#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""

#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""

msgid "Time"
msgstr "Akud"

msgid "URL"
msgstr "URL"

msgid "Raw binary data"
msgstr ""

#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr ""

msgid "Universally unique identifier"
msgstr ""

msgid "File"
msgstr "Afaylu"

msgid "Image"
msgstr "Tugna"

#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr ""

msgid "Foreign Key (type determined by related field)"
msgstr ""

msgid "One-to-one relationship"
msgstr ""

#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr ""

#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr ""

msgid "Many-to-many relationship"
msgstr ""

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the
#. label
msgid ":?.!"
msgstr ":?.!"

msgid "This field is required."
msgstr ""

msgid "Enter a whole number."
msgstr "Sekcem amḍan ummid."

msgid "Enter a valid date."
msgstr ""

msgid "Enter a valid time."
msgstr ""

msgid "Enter a valid date/time."
msgstr ""

msgid "Enter a valid duration."
msgstr ""

#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr ""

msgid "No file was submitted. Check the encoding type on the form."
msgstr ""

msgid "No file was submitted."
msgstr "Afaylu ur yettwazen ara."

msgid "The submitted file is empty."
msgstr ""

#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""
msgstr[1] ""

msgid "Please either submit a file or check the clear checkbox, not both."
msgstr ""

msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""

#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr ""

msgid "Enter a list of values."
msgstr ""

msgid "Enter a complete value."
msgstr "Sekcem azal ummid."

msgid "Enter a valid UUID."
msgstr ""

#. Translators: This is the default suffix added to form field labels
msgid ":"
msgstr ":"

#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr ""

msgid "ManagementForm data is missing or has been tampered with"
msgstr ""

#, python-format
msgid "Please submit %d or fewer forms."
msgid_plural "Please submit %d or fewer forms."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Please submit %d or more forms."
msgid_plural "Please submit %d or more forms."
msgstr[0] ""
msgstr[1] ""

msgid "Order"
msgstr "Amizwer"

msgid "Delete"
msgstr "KKES"

#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr ""

#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""

#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""

msgid "Please correct the duplicate values below."
msgstr ""

msgid "The inline value did not match the parent instance."
msgstr ""

msgid "Select a valid choice. That choice is not one of the available choices."
msgstr ""

#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr ""

#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""

msgid "Clear"
msgstr "Sfeḍ"

msgid "Currently"
msgstr "Tura"

msgid "Change"
msgstr "Beddel"

msgid "Unknown"
msgstr "Arussin"

msgid "Yes"
msgstr "Ih"

msgid "No"
msgstr "Uhu"

msgid "Year"
msgstr ""

msgid "Month"
msgstr ""

msgid "Day"
msgstr ""

msgid "yes,no,maybe"
msgstr ""

#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%s KB"
msgstr "%s KAṬ"

#, python-format
msgid "%s MB"
msgstr "%s MAṬ"

#, python-format
msgid "%s GB"
msgstr "%s GAṬ"

#, python-format
msgid "%s TB"
msgstr "%s TAṬ"

#, python-format
msgid "%s PB"
msgstr "%s PAṬ"

msgid "p.m."
msgstr "m.d."

msgid "a.m."
msgstr "f.t."

msgid "PM"
msgstr "MD"

msgid "AM"
msgstr "FT"

msgid "midnight"
msgstr "ttnaṣfa n yiḍ"

msgid "noon"
msgstr "ttnaṣfa n uzal"

msgid "Monday"
msgstr "Arim"

msgid "Tuesday"
msgstr "Aram"

msgid "Wednesday"
msgstr "Ahad"

msgid "Thursday"
msgstr "Amhad"

msgid "Friday"
msgstr "Sem"

msgid "Saturday"
msgstr "Sed"

msgid "Sunday"
msgstr "Acer"

msgid "Mon"
msgstr "Ari"

msgid "Tue"
msgstr "Ara"

msgid "Wed"
msgstr "Aha"

msgid "Thu"
msgstr "Amh"

msgid "Fri"
msgstr "Sem"

msgid "Sat"
msgstr "Sed"

msgid "Sun"
msgstr "Ace"

msgid "January"
msgstr "Yennayer"

msgid "February"
msgstr "Fuṛaṛ"

msgid "March"
msgstr "Meɣres"

msgid "April"
msgstr "Yebrir"

msgid "May"
msgstr "Mayyu"

msgid "June"
msgstr "Yunyu"

msgid "July"
msgstr "Yulyu"

msgid "August"
msgstr "Ɣuct"

msgid "September"
msgstr "Ctamber"

msgid "October"
msgstr "Tuber"

msgid "November"
msgstr "Wamber"

msgid "December"
msgstr "Dujamber"

msgid "jan"
msgstr "yen"

msgid "feb"
msgstr "fuṛ"

msgid "mar"
msgstr "meɣ"

msgid "apr"
msgstr "yeb"

msgid "may"
msgstr "may"

msgid "jun"
msgstr "yun"

msgid "jul"
msgstr "yul"

msgid "aug"
msgstr "ɣuc"

msgid "sep"
msgstr "cte"

msgid "oct"
msgstr "tub"

msgid "nov"
msgstr "wam"

msgid "dec"
msgstr "duj"

msgctxt "abbrev. month"
msgid "Jan."
msgstr "Yen."

msgctxt "abbrev. month"
msgid "Feb."
msgstr "Fuṛ."

msgctxt "abbrev. month"
msgid "March"
msgstr "Meɣres"

msgctxt "abbrev. month"
msgid "April"
msgstr "Yebrir"

msgctxt "abbrev. month"
msgid "May"
msgstr "Mayyu"

msgctxt "abbrev. month"
msgid "June"
msgstr "Yunyu"

msgctxt "abbrev. month"
msgid "July"
msgstr "Yulyu"

msgctxt "abbrev. month"
msgid "Aug."
msgstr "Ɣuc."

msgctxt "abbrev. month"
msgid "Sept."
msgstr ""

msgctxt "abbrev. month"
msgid "Oct."
msgstr "Tub."

msgctxt "abbrev. month"
msgid "Nov."
msgstr "Wam."

msgctxt "abbrev. month"
msgid "Dec."
msgstr "Duj."

msgctxt "alt. month"
msgid "January"
msgstr "Yennayer"

msgctxt "alt. month"
msgid "February"
msgstr "Fuṛaṛ"

msgctxt "alt. month"
msgid "March"
msgstr "Meɣres"

msgctxt "alt. month"
msgid "April"
msgstr "Yebrir"

msgctxt "alt. month"
msgid "May"
msgstr "Mayyu"

msgctxt "alt. month"
msgid "June"
msgstr "Yunyu"

msgctxt "alt. month"
msgid "July"
msgstr "Yulyu"

msgctxt "alt. month"
msgid "August"
msgstr "Ɣuct"

msgctxt "alt. month"
msgid "September"
msgstr "Ctamber"

msgctxt "alt. month"
msgid "October"
msgstr "Tuber"

msgctxt "alt. month"
msgid "November"
msgstr "Wamber"

msgctxt "alt. month"
msgid "December"
msgstr "Dujamber"

msgid "This is not a valid IPv6 address."
msgstr ""

#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr ""

msgid "or"
msgstr "neɣ"

#. Translators: This string is used as a separator between list elements
msgid ", "
msgstr ", "

#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

msgid "0 minutes"
msgstr "0 n tisdatin"

msgid "Forbidden"
msgstr "Yegdel"

msgid "CSRF verification failed. Request aborted."
msgstr ""

msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your Web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""

msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""

msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a rel=\"noreferrer"
"\" …> for links to third-party sites."
msgstr ""

msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""

msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""

msgid "More information is available with DEBUG=True."
msgstr ""

msgid "No year specified"
msgstr ""

msgid "Date out of range"
msgstr ""

msgid "No month specified"
msgstr ""

msgid "No day specified"
msgstr ""

msgid "No week specified"
msgstr ""

#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr ""

#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because %(class_name)s."
"allow_future is False."
msgstr ""

#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr ""

#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr ""

msgid "Page is not “last”, nor can it be converted to an int."
msgstr ""

#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr ""

#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr ""

msgid "Directory indexes are not allowed here."
msgstr ""

#, python-format
msgid "“%(path)s” does not exist"
msgstr ""

#, python-format
msgid "Index of %(directory)s"
msgstr ""

msgid "Django: the Web framework for perfectionists with deadlines."
msgstr ""

#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""

msgid "The install worked successfully! Congratulations!"
msgstr ""

#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" rel=\"noopener"
"\">DEBUG=True</a> is in your settings file and you have not configured any "
"URLs."
msgstr ""

msgid "Django Documentation"
msgstr ""

msgid "Topics, references, &amp; how-to’s"
msgstr ""

msgid "Tutorial: A Polling App"
msgstr ""

msgid "Get started with Django"
msgstr "Bdu s Django"

msgid "Django Community"
msgstr ""

msgid "Connect, get help, or contribute"
msgstr ""
